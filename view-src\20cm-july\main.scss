.popout-trading-window {

	.trade-view-block {
		height: 100%;
		padding-bottom: 36px;
		box-sizing: border-box;
	}
}

.view-main-inter {

	height: 100%;
	display: flex;
	flex-direction: column;
	box-sizing: border-box;

	&.full-occupied {
		
		padding-bottom: 0;
		.part-bottom {
			display: none;
		}
	}

    .view-layout {

		box-sizing: border-box;
		height: 100%;
		width: 100%;
		padding-top: 28px;
		background-color: #1A212B;
	}

	.view-toolbar {

		position: relative;
		height: 28px;
		// margin-top: -28px;
		line-height: 28px;
		overflow: hidden;
		background-color: #253650;

		.el-input,
		.el-select,
		.el-autocomplete,
		.el-input__inner {
			height: 24px;
		}

		.el-input__icon {
			line-height: 24px;
		}

		.fixed-right-icon {
			position: absolute;
			right: 0;
			top: 0;
			z-index: 1;
		}
	}

    .regular-view {

		box-sizing: border-box;
		padding: 2px;
	}

	.part-top {

		flex-grow: 1;
		flex-shrink: 1;
		height: 99px;
		box-sizing: border-box;
	}
	
	.part-bottom {
		
		flex-grow: 0;
		flex-shrink: 0;
		height: 290px;
		display: flex;
		justify-content: flex-start;
		box-sizing: border-box;
	}

	.view-level2 {

		height: 100%;
		width: 148px;
		flex-grow: 0;
		flex-shrink: 0;
		box-sizing: border-box;

		.level-item {

			height: 23px;
			line-height: 23px;
			padding-left: 10px;
			padding-right: 10px;

			&:hover {
				background-color: lightsalmon;
			}

			> * {

				display: block;
				float: left;
			}
		}

		.level-item-0 {
			margin-top: 10px;
		}

		.level-item-4 {
			margin-bottom: 5px;
		}

		.level-item-5 {

			padding-top: 5px;
			border-top: 1px solid #000;
		}

		.level-name {
			width: 22px;
		}

		.level-price {

			width: 45px;
			text-align: right;
		}

		.level-hands {

			width: 60px;
			text-align: right;
		}
	}

	.view-trading {

		flex-grow: 0;
		flex-shrink: 0;
		height: 100%;
		width: 300px;
		box-sizing: border-box;
		border-left: 4px solid #0C1016;
	}

	.trading-panel {

		padding: 0 10px 10px 10px;

		.el-radio-group {
			width: 100%;
		}

		.el-radio-button {
			width: 50%;
		}

		.el-radio-button__inner {
			width: 100%;
		}

    .flex-row {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .input-row {
        & + .input-row {
          margin-left: 5px;
        }
      }

      .save-auto {
        margin-right: 10px;
        margin-top: 10px;
        width: 50%;
		&.is-disabled {
			background-color: #a0cfff !important;
			border-color: #a0cfff !important;
		}
      }
      .trade-btn {
        width: 50%;
      }
    }

		.input-row {
			margin-top: 10px;
			height: 24px;
			display: flex;
      align-items: center;
		}

		.input-text {
			display: block;
			line-height: 28px;
      margin-right: 5px;
		}

		.input-ctr {
			flex: 1;
      min-width: 1px;
      display: flex;
      align-items: center;
			&.with-unit {

				// padding-right: 25px;

				.ctr-unit {

					display: inline-block;
					// margin-right: -25px;
					width: 25px;
					text-align: center;
				}
			}

      .el-input-number {
        width: 100%;
      }
		}

		.link-btn {

			cursor: default;
			&:hover {
				text-decoration: underline;
			}
		}

		.ratios .link-btn {

			display: block;
			float: left;
			width: 16%;
			font-size: 14px;
		}

		.custom-ratio {

			display: inline-block;
			position: relative;

			.el-input-number {

				position: relative;
				top: 0;
				margin-left: 4px;
				width: 60px;
			}

			.el-input__inner {

				padding-left: 5px;
    			padding-right: 5px;
				border-radius: 4px;
			}
		}
	}

	.by-volume-flag,
	.by-amount-flag {
		
		background-color: #245BB0;
		border-radius: 4px;
		width: 34px;
		text-align: center;
	}

	.by-amount-flag {
		color: red;
	}

	.concentrated-input {

		.el-input__inner {
			letter-spacing: 1px;
			font-size: 16px;
		}

        &.single {
			.el-input__inner {
				padding: 0 10px;
			}
		}
	}

    .total-buy {
    .el-input__inner {
      letter-spacing: 1px;
      font-size: 16px;
    }
  }

  .sell-position {
    .el-input__inner {
      width: 40px;
      padding: 0 4px;
    }
  }

  .interval-input {
    .el-input__inner {
      width: 54px;
      padding: 0 4px;
    }
  }

	.cancel-option-ctr {

		margin: 0 4px;
		width: 120px;

		.el-input__inner {
			height: 24px;
      width: 28px;
      padding: 0 4px;
		}
	}

	.view-sell-task-ext {

		min-width: 100px;
		width: 100px;
		flex-grow: 1;
		flex-shrink: 0;
		padding-left: 2px;
		overflow-y: hidden;
		overflow-x: auto;
		box-sizing: border-box;

		.view-sell-task {

			padding-top: 28px;
			box-sizing: border-box;

			.title {

				height: 28px;
				margin-top: -28px;
				line-height: 28px;
				background-color: #253650;
			}
		}
	}

	.tabbed-radios-external {

		.el-radio-group {
			margin-bottom: -1px;
		}

		.el-radio-button__inner {

			border-top-left-radius: 6px;
			border-top-right-radius: 6px;
			border: none;
			background-color: transparent;
		}

		.el-radio-button__orig-radio:checked+.el-radio-button__inner {
			background-color: #1A212B;
		}

		.el-radio-button--mini .el-radio-button__inner {
			padding: 5px 15px;
		}
	}
}