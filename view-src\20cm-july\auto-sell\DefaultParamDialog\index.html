<el-dialog custom-class="algorithm-dialog" width="400px" :title="title" :visible.sync="visible" :show-close="false" :close-on-click-modal="false" @close="cancel">
  <div class="s-pd-10" style="padding-left: 0">
    <el-form size="mini" class="form" label-width="150px" ref="form" :model="form" :rules="rules">
      <el-form-item label="二次确认" prop="confirmTwice">
        <div class="form-label" slot="label">
          <div class="label">二次确认</div>
          <el-tooltip placement="top" content="启用时，保存并启动卖出算法时会弹出二次确认界面">
            <i class="label-icon el-icon-question"></i>
          </el-tooltip>
        </div>
        <el-switch v-model="form.confirmTwice"></el-switch>
      </el-form-item>
      <el-form-item label="算法名称" prop="algorithmType">
        <el-select v-model="form.algorithmType" placeholder="请选择算法" @change="handleChangeAlgorithmType">
          <el-option v-for="item in algorithmTypes" :key="item.value" :label="item.label" :value="item.value">
            <el-tooltip placement="top" :content="item.desp">
              <i class="option-icon el-icon-question"></i>
            </el-tooltip>
            <span style="margin-left: 5px" class="option-label">{{ item.label }}</span>
          </el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <div class="param-setting">
      <!-- <div class="hint-row">
        <div class="title">参数设置</div>
        <div v-if="!form.algorithmType" class="hint">请先选择算法</div>
      </div> -->
      <div v-if="!!form.algorithmType">
        <div class="param-list">
          <el-form :key="form.algorithmType" size="mini" class="form" label-width="150px" ref="paramForm" :model="paramForm" :rules="paramRules">
            <el-form-item v-for="(item, index) in displayParams" :key="index" :label="item.label" :prop="item.prop">
              <div class="form-label" slot="label">
                <div class="label">{{ item.label }}</div>
                <el-tooltip placement="top" :content="item.desp">
                  <i class="label-icon el-icon-question"></i>
                </el-tooltip>
              </div>
              <el-select v-if="item.type === 'select'" v-model="paramForm[item.prop]" :placeholder="item.placeholder" @change="updateSetting">
                <el-option v-for="(option, optionIndex) in item.options" :key="optionIndex" :label="option.label" :value="option.value"></el-option>
              </el-select>
              <div v-else-if="item.type === 'number'" class="number-wrapper">
                <el-input-number :controls="false" v-model="paramForm[item.prop]" :placeholder="item.placeholder" :min="item.min" :max="item.max" @change="updateSetting"> </el-input-number>
                <div v-if="item.unit" class="unit">{{ item.unit }}</div>
              </div>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>
  </div>

  <div slot="footer">
    <el-button @click="cancel" type="info" size="mini">取消</el-button>
    <el-button @click="reset" type="primary" size="mini">重置</el-button>
    <el-button @click="save" type="primary" size="mini">保存</el-button>
  </div>
</el-dialog>
